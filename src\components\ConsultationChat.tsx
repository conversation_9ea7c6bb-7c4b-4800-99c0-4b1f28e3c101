
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Send, X, Bot, Settings } from 'lucide-react';
import { getAPIService, ChatMessage } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';

interface Agent {
  id: string;
  name: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  personality: string;
  expertise: string[];
}

interface Message {
  id: string;
  sender: 'user' | 'agent';
  content: string;
  timestamp: string;
}

interface ConsultationChatProps {
  agent: Agent;
  onClose: () => void;
  onOpenSettings?: () => void;
}

const ConsultationChat = ({ agent, onClose, onOpenSettings }: ConsultationChatProps) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'agent',
      content: `Hello! I'm ${agent.name}, your ${agent.title}. ${agent.personality} How can I help you today?`,
      timestamp: 'now'
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const { toast } = useToast();

  const generateResponse = async (userMessage: string): Promise<string> => {
    const apiService = getAPIService();
    
    if (!apiService) {
      return "I need an API configuration to provide intelligent responses. Please configure your AI provider in settings.";
    }

    // Create specialized system prompt for each agent
    const systemPrompt = `You are ${agent.name}, a ${agent.title}. ${agent.personality}

Your areas of expertise include: ${agent.expertise.join(', ')}.

Guidelines for your responses:
- Stay in character as ${agent.name}
- Focus on your area of expertise: ${agent.title}
- Provide practical, actionable advice
- Be professional but personable
- Keep responses concise and relevant
- If asked about topics outside your expertise, acknowledge it and suggest consulting other team members

User's question: ${userMessage}`;

    const chatMessages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ];

    try {
      const response = await apiService.sendMessage(chatMessages);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      return response.content;
    } catch (error) {
      console.error('API Error:', error);
      return `I'm having trouble connecting to the AI service. Error: ${error instanceof Error ? error.message : 'Unknown error'}. Please check your API configuration.`;
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const userMsg: Message = {
      id: Date.now().toString(),
      sender: 'user',
      content: newMessage,
      timestamp: 'now'
    };

    setMessages(prev => [...prev, userMsg]);
    const currentMessage = newMessage;
    setNewMessage('');
    setIsTyping(true);

    try {
      const responseContent = await generateResponse(currentMessage);
      
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'agent',
        content: responseContent,
        timestamp: 'now'
      };
      
      setMessages(prev => [...prev, agentResponse]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to get response from AI service. Please check your API configuration.",
        variant: "destructive"
      });
    } finally {
      setIsTyping(false);
    }
  };

  const IconComponent = agent.icon;

  return (
    <Card className="fixed inset-4 z-50 bg-white shadow-2xl border-2">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <div className={`bg-gradient-to-r ${agent.color} w-full h-full rounded-full flex items-center justify-center`}>
                <IconComponent className="h-5 w-5 text-white" />
              </div>
              <AvatarFallback>{agent.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{agent.name}</CardTitle>
              <Badge variant="secondary" className="text-xs">{agent.title}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {onOpenSettings && (
              <Button variant="ghost" size="sm" onClick={onOpenSettings}>
                <Settings className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-96 p-0">
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[70%] p-3 rounded-lg ${
                message.sender === 'user' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-900'
              }`}>
                {message.sender === 'agent' && (
                  <div className="flex items-center gap-2 mb-1">
                    <Bot className="h-3 w-3" />
                    <span className="text-xs font-medium">{agent.name}</span>
                  </div>
                )}
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <Bot className="h-3 w-3" />
                  <span className="text-xs">{agent.name} is thinking...</span>
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="border-t p-4">
          <div className="flex space-x-2">
            <Textarea
              placeholder={`Ask ${agent.name} anything...`}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="min-h-[60px]"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || isTyping}
              className={`bg-gradient-to-r ${agent.color} hover:opacity-90`}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConsultationChat;
