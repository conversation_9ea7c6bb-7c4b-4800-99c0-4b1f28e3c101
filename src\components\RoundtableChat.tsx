
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Send, Users, Crown, TrendingUp, Megaphone, DollarSign, Monitor } from 'lucide-react';
import { toast } from 'sonner';

const RoundtableChat = () => {
  const [message, setMessage] = useState('');
  const [isDiscussing, setIsDiscussing] = useState(false);

  const participants = [
    { id: 'director', name: 'General Director', icon: Crown, color: 'from-amber-500 to-orange-500', role: 'Strategic Oversight' },
    { id: 'sarah', name: '<PERSON>', icon: TrendingUp, color: 'from-green-500 to-emerald-600', role: 'Sales Director' },
    { id: 'mike', name: '<PERSON>', icon: Megaphone, color: 'from-purple-500 to-pink-600', role: 'Marketing Director' },
    { id: 'david', name: '<PERSON>', icon: DollarSign, color: 'from-blue-500 to-cyan-600', role: 'CFO' },
    { id: 'alex', name: 'Alex Thompson', icon: Monitor, color: 'from-slate-500 to-gray-600', role: 'IT Director' },
  ];

  const conversationHistory = [
    {
      id: 1,
      participant: 'director',
      message: "Team, we need to discuss our Q1 strategy. What are your initial thoughts on market expansion?",
      timestamp: '2 minutes ago'
    },
    {
      id: 2,
      participant: 'sarah',
      message: "From a sales perspective, I see 30% growth potential in the SMB segment. Our conversion rates there are promising.",
      timestamp: '1 minute ago'
    },
    {
      id: 3,
      participant: 'mike',
      message: "I agree with Sarah. Our brand awareness in that segment is growing. We should align our content strategy to support this push.",
      timestamp: '1 minute ago'
    },
    {
      id: 4,
      participant: 'david',
      message: "The numbers support this direction. We have the budget allocated, and ROI projections look solid at 3:1 for targeted campaigns.",
      timestamp: '30 seconds ago'
    }
  ];

  const handleStartDiscussion = async () => {
    if (!message.trim()) {
      toast.error('Please enter a topic for discussion');
      return;
    }

    setIsDiscussing(true);
    
    // Simulate roundtable discussion
    setTimeout(() => {
      toast.success('Roundtable discussion initiated. All department heads are contributing their insights.');
      setMessage('');
      setIsDiscussing(false);
    }, 3000);
  };

  const getParticipant = (participantId: string) => {
    return participants.find(p => p.id === participantId);
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Executive Roundtable</h2>
        <p className="text-gray-600">Collaborative discussions with your entire AI executive team</p>
      </div>

      {/* Active Participants */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Active Participants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {participants.map((participant) => {
              const IconComponent = participant.icon;
              return (
                <div key={participant.id} className="text-center">
                  <Avatar className="h-12 w-12 mx-auto mb-2">
                    <div className={`bg-gradient-to-r ${participant.color} w-full h-full rounded-full flex items-center justify-center`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <AvatarFallback>{participant.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <p className="text-sm font-medium text-gray-900">{participant.name}</p>
                  <Badge variant="secondary" className="text-xs">{participant.role}</Badge>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Conversation History */}
      <Card>
        <CardHeader>
          <CardTitle>Discussion Thread</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {conversationHistory.map((msg) => {
              const participant = getParticipant(msg.participant);
              if (!participant) return null;
              
              const IconComponent = participant.icon;
              return (
                <div key={msg.id} className="flex items-start space-x-3">
                  <Avatar className="h-8 w-8">
                    <div className={`bg-gradient-to-r ${participant.color} w-full h-full rounded-full flex items-center justify-center`}>
                      <IconComponent className="h-4 w-4 text-white" />
                    </div>
                    <AvatarFallback>{participant.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm text-gray-900">{participant.name}</span>
                      <span className="text-xs text-gray-500">{msg.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{msg.message}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* New Discussion Input */}
      <Card>
        <CardHeader>
          <CardTitle>Start New Discussion</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Present a strategic question or topic for roundtable discussion... e.g., 'How should we approach international expansion?' or 'What's our competitive response to the new market entrant?'"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="min-h-[120px]"
          />
          <Button
            onClick={handleStartDiscussion}
            disabled={isDiscussing}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {isDiscussing ? (
              <>
                <Users className="h-4 w-4 mr-2 animate-pulse" />
                Discussion in Progress...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Start Roundtable Discussion
              </>
            )}
          </Button>
          {isDiscussing && (
            <div className="text-center text-sm text-gray-600">
              All department heads are analyzing and preparing their responses...
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RoundtableChat;
