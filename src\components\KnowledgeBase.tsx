
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Upload, FileText, Search, TrendingUp, Users, Shield, Database } from 'lucide-react';
import { toast } from 'sonner';

const KnowledgeBase = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const documents = [
    { id: 1, name: 'Company Strategy 2024.pdf', category: 'Strategy', size: '2.4 MB', lastModified: '2 days ago', status: 'processed' },
    { id: 2, name: 'Sales Process Manual.docx', category: 'Sales', size: '1.8 MB', lastModified: '1 week ago', status: 'processed' },
    { id: 3, name: 'Marketing Playbook.pdf', category: 'Marketing', size: '3.2 MB', lastModified: '3 days ago', status: 'processed' },
    { id: 4, name: 'Financial Guidelines.xlsx', category: 'Finance', size: '956 KB', lastModified: '5 days ago', status: 'processed' },
    { id: 5, name: 'IT Security Policy.pdf', category: 'IT', size: '1.2 MB', lastModified: '1 week ago', status: 'processing' },
    { id: 6, name: 'Customer Feedback Q4.pdf', category: 'Research', size: '4.1 MB', lastModified: '1 day ago', status: 'processed' },
  ];

  const categories = [
    { name: 'Strategy', count: 15, icon: TrendingUp, color: 'bg-blue-100 text-blue-800' },
    { name: 'Sales', count: 23, icon: Users, color: 'bg-green-100 text-green-800' },
    { name: 'Marketing', count: 31, icon: TrendingUp, color: 'bg-purple-100 text-purple-800' },
    { name: 'Finance', count: 18, icon: Database, color: 'bg-blue-100 text-blue-800' },
    { name: 'IT', count: 12, icon: Shield, color: 'bg-gray-100 text-gray-800' },
    { name: 'Research', count: 27, icon: FileText, color: 'bg-amber-100 text-amber-800' },
  ];

  const stats = [
    { label: 'Total Documents', value: '247', change: '+12 this week' },
    { label: 'Processing Queue', value: '3', change: 'All current' },
    { label: 'Storage Used', value: '2.8 GB', change: 'of 10 GB plan' },
    { label: 'AI Insights Generated', value: '1,342', change: '+89 today' },
  ];

  const handleFileUpload = async () => {
    setIsUploading(true);
    
    // Simulate file upload process
    setTimeout(() => {
      toast.success('Documents uploaded successfully and added to the knowledge base');
      setIsUploading(false);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    return status === 'processed' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-amber-100 text-amber-800';
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Business Knowledge Base</h2>
        <p className="text-gray-600">Centralized repository powering your AI executive team's expertise</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="text-sm font-medium text-gray-600">{stat.label}</div>
              <div className="text-xs text-gray-500 mt-1">{stat.change}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Document Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Document Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {categories.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <category.icon className="h-5 w-5 text-gray-600" />
                    <span className="font-medium text-gray-900">{category.name}</span>
                  </div>
                  <Badge className={category.color}>{category.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upload Documents */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Documents</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-sm font-medium text-gray-900 mb-2">Drop files here or click to browse</p>
              <p className="text-xs text-gray-500 mb-4">PDF, DOC, XLS, TXT files up to 10MB</p>
              <Button 
                onClick={handleFileUpload}
                disabled={isUploading}
                className="w-full"
              >
                {isUploading ? 'Uploading...' : 'Choose Files'}
              </Button>
            </div>
            
            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Processing documents...</span>
                  <span>75%</span>
                </div>
                <Progress value={75} className="w-full" />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>Search Knowledge Base</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents, insights, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button className="w-full" variant="outline">
              Advanced Search
            </Button>
            
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Searches</h4>
              <div className="space-y-1">
                <div className="text-xs text-gray-600 hover:text-blue-600 cursor-pointer">competitor analysis</div>
                <div className="text-xs text-gray-600 hover:text-blue-600 cursor-pointer">marketing ROI</div>
                <div className="text-xs text-gray-600 hover:text-blue-600 cursor-pointer">budget allocation</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Document Library */}
      <Card>
        <CardHeader>
          <CardTitle>Document Library</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {documents.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center space-x-3">
                  <FileText className="h-8 w-8 text-gray-600" />
                  <div>
                    <p className="font-medium text-gray-900">{doc.name}</p>
                    <p className="text-sm text-gray-500">{doc.size} • Modified {doc.lastModified}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge variant="secondary">{doc.category}</Badge>
                  <Badge className={getStatusColor(doc.status)}>
                    {doc.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default KnowledgeBase;
