
import { APIConfig } from '@/components/APISettings';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatResponse {
  content: string;
  error?: string;
}

export class APIService {
  private config: APIConfig;

  constructor(config: APIConfig) {
    this.config = config;
  }

  async sendMessage(messages: ChatMessage[]): Promise<ChatResponse> {
    try {
      switch (this.config.provider) {
        case 'openai':
          return await this.sendToOpenAI(messages);
        case 'anthropic':
          return await this.sendToAnthropic(messages);
        case 'gemini':
          return await this.sendToGemini(messages);
        case 'lmstudio':
          return await this.sendToLMStudio(messages);
        case 'litellm':
          return await this.sendToLiteLLM(messages);
        case 'ollama':
          return await this.sendToOllama(messages);
        default:
          throw new Error(`Unsupported provider: ${this.config.provider}`);
      }
    } catch (error) {
      console.error('API Service Error:', error);
      return {
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async sendToOpenAI(messages: ChatMessage[]): Promise<ChatResponse> {
    const response = await fetch(this.config.endpoint || 'https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || 'No response generated'
    };
  }

  private async sendToAnthropic(messages: ChatMessage[]): Promise<ChatResponse> {
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role !== 'system');

    const response = await fetch(this.config.endpoint || 'https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: 1000,
        system: systemMessage?.content || '',
        messages: userMessages
      })
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.content[0]?.text || 'No response generated'
    };
  }

  private async sendToGemini(messages: ChatMessage[]): Promise<ChatResponse> {
    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n');
    
    const response = await fetch(
      `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      }
    );

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.candidates[0]?.content?.parts[0]?.text || 'No response generated'
    };
  }

  private async sendToLMStudio(messages: ChatMessage[]): Promise<ChatResponse> {
    const response = await fetch(this.config.endpoint || 'http://localhost:1234/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`LM Studio API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || 'No response generated'
    };
  }

  private async sendToLiteLLM(messages: ChatMessage[]): Promise<ChatResponse> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    const response = await fetch(this.config.endpoint || 'http://localhost:4000/chat/completions', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`LiteLLM API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || 'No response generated'
    };
  }

  private async sendToOllama(messages: ChatMessage[]): Promise<ChatResponse> {
    const prompt = messages.map(m => m.content).join('\n');
    
    const response = await fetch(this.config.endpoint || 'http://localhost:11434/api/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        prompt: prompt,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.response || 'No response generated'
    };
  }
}

export const getAPIService = (): APIService | null => {
  try {
    const configStr = localStorage.getItem('current-api-config');
    if (!configStr) {
      return null;
    }
    
    const config: APIConfig = JSON.parse(configStr);
    return new APIService(config);
  } catch (error) {
    console.error('Error creating API service:', error);
    return null;
  }
};
