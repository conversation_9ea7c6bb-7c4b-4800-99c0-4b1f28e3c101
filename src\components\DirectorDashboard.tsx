
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Crown, Send, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

const DirectorDashboard = () => {
  const [instruction, setInstruction] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const recentTasks = [
    { id: 1, task: 'Analyze Q4 sales performance', department: 'Sales', status: 'completed', agent: '<PERSON>' },
    { id: 2, task: 'Review marketing campaign ROI', department: 'Marketing', status: 'in-progress', agent: '<PERSON>' },
    { id: 3, task: 'Budget allocation for next quarter', department: 'Finance', status: 'pending', agent: '<PERSON>' },
    { id: 4, task: 'Security audit recommendations', department: 'IT', status: 'completed', agent: '<PERSON>' },
  ];

  const handleDelegateTask = async () => {
    if (!instruction.trim()) {
      toast.error('Please enter an instruction');
      return;
    }

    setIsProcessing(true);
    
    // Simulate AI processing
    setTimeout(() => {
      toast.success('Task delegated successfully to the appropriate department');
      setInstruction('');
      setIsProcessing(false);
    }, 2000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in-progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-amber-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-amber-100 text-amber-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* General Director Card */}
      <Card className="border-l-4 border-l-amber-500">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <div className="bg-gradient-to-r from-amber-500 to-orange-500 w-full h-full rounded-full flex items-center justify-center">
                <Crown className="h-6 w-6 text-white" />
              </div>
              <AvatarFallback>GD</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="flex items-center gap-2">
                General Director
                <Badge variant="secondary" className="bg-amber-100 text-amber-800">
                  Chief Executive Agent
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Your strategic AI advisor and task delegation expert
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Executive Summary</h3>
            <p className="text-sm text-gray-700">
              Good morning! I'm monitoring all departments and ready to help you make strategic decisions. 
              Your business metrics are trending positively with sales up 12% and customer satisfaction at 94%.
            </p>
          </div>
          
          <div className="space-y-3">
            <label className="text-sm font-medium text-gray-700">
              Give me an instruction to delegate to the team:
            </label>
            <Textarea
              placeholder="e.g., 'Analyze our competitor's pricing strategy and recommend adjustments' or 'Prepare a comprehensive IT security assessment'"
              value={instruction}
              onChange={(e) => setInstruction(e.target.value)}
              className="min-h-[100px]"
            />
            <Button 
              onClick={handleDelegateTask}
              disabled={isProcessing}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isProcessing ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Processing & Delegating...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Delegate Task
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Tasks */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Delegated Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(task.status)}
                  <div>
                    <p className="font-medium text-gray-900">{task.task}</p>
                    <p className="text-sm text-gray-500">Assigned to {task.agent} - {task.department}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(task.status)}>
                  {task.status.replace('-', ' ')}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DirectorDashboard;
