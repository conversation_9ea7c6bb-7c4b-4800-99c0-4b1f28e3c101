
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Eye, EyeOff, Save, Trash2, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export interface APIProvider {
  id: string;
  name: string;
  endpoint: string;
  models: string[];
  requiresKey: boolean;
}

export interface APIConfig {
  provider: string;
  apiKey: string;
  endpoint?: string;
  model: string;
}

const API_PROVIDERS: APIProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    requiresKey: true
  },
  {
    id: 'anthropic',
    name: '<PERSON><PERSON><PERSON> (<PERSON>)',
    endpoint: 'https://api.anthropic.com/v1/messages',
    models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
    requiresKey: true
  },
  {
    id: 'gemini',
    name: 'Google Gemini',
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/',
    models: ['gemini-pro', 'gemini-pro-vision'],
    requiresKey: true
  },
  {
    id: 'lmstudio',
    name: 'LM Studio',
    endpoint: 'http://localhost:1234/v1/chat/completions',
    models: ['local-model'],
    requiresKey: false
  },
  {
    id: 'litellm',
    name: 'LiteLLM',
    endpoint: 'http://localhost:4000/chat/completions',
    models: ['gpt-3.5-turbo', 'claude-3-sonnet-20240229'],
    requiresKey: true
  },
  {
    id: 'ollama',
    name: 'Ollama',
    endpoint: 'http://localhost:11434/api/chat',
    models: ['llama2', 'codellama', 'mistral'],
    requiresKey: false
  }
];

const APISettings = () => {
  const [config, setConfig] = useState<APIConfig>({
    provider: 'openai',
    apiKey: '',
    model: 'gpt-4'
  });
  const [customEndpoint, setCustomEndpoint] = useState('');
  const [showKey, setShowKey] = useState(false);
  const [savedConfigs, setSavedConfigs] = useState<Record<string, APIConfig>>({});
  const { toast } = useToast();

  useEffect(() => {
    // Load saved configurations from localStorage
    const saved = localStorage.getItem('api-configs');
    if (saved) {
      try {
        const configs = JSON.parse(saved);
        setSavedConfigs(configs);
        
        // Load the last used configuration
        const lastUsed = localStorage.getItem('last-used-api');
        if (lastUsed && configs[lastUsed]) {
          setConfig(configs[lastUsed]);
          if (configs[lastUsed].endpoint) {
            setCustomEndpoint(configs[lastUsed].endpoint);
          }
        }
      } catch (error) {
        console.error('Error loading API configs:', error);
      }
    }
  }, []);

  const selectedProvider = API_PROVIDERS.find(p => p.id === config.provider);

  const handleProviderChange = (providerId: string) => {
    const provider = API_PROVIDERS.find(p => p.id === providerId);
    if (provider) {
      setConfig(prev => ({
        ...prev,
        provider: providerId,
        model: provider.models[0],
        endpoint: provider.endpoint
      }));
      setCustomEndpoint(provider.endpoint);
    }
  };

  const handleSaveConfig = () => {
    const configToSave = {
      ...config,
      endpoint: customEndpoint || selectedProvider?.endpoint
    };

    const newSavedConfigs = {
      ...savedConfigs,
      [config.provider]: configToSave
    };

    setSavedConfigs(newSavedConfigs);
    localStorage.setItem('api-configs', JSON.stringify(newSavedConfigs));
    localStorage.setItem('last-used-api', config.provider);
    localStorage.setItem('current-api-config', JSON.stringify(configToSave));

    toast({
      title: "Configuration Saved",
      description: `${selectedProvider?.name} configuration has been saved successfully.`
    });
  };

  const handleDeleteConfig = (providerId: string) => {
    const newSavedConfigs = { ...savedConfigs };
    delete newSavedConfigs[providerId];
    setSavedConfigs(newSavedConfigs);
    localStorage.setItem('api-configs', JSON.stringify(newSavedConfigs));

    toast({
      title: "Configuration Deleted",
      description: `Configuration for ${API_PROVIDERS.find(p => p.id === providerId)?.name} has been deleted.`
    });
  };

  const handleTestConnection = async () => {
    let response;
    let providerName = selectedProvider?.name || config.provider;
    toast({
      title: "Testing Connection...",
      description: `Trying to connect to ${providerName}. Please wait.`,
    });

    try {
      // Handle all providers in the switch
      switch (config.provider) {
        case "openai":
          response = await fetch(customEndpoint || "https://api.openai.com/v1/models", {
            method: "GET",
            headers: {
              Authorization: `Bearer ${config.apiKey}`,
            },
          });
          break;

        case "anthropic":
          response = await fetch(customEndpoint || "https://api.anthropic.com/v1/models", {
            method: "GET",
            headers: {
              "x-api-key": config.apiKey,
              "anthropic-version": "2023-06-01", // adjust according to Anthropic docs if needed
            },
          });
          break;

        case "gemini": {
          // For Gemini, API key goes as a query parameter `key`
          const endpoint =
            (customEndpoint || "https://generativelanguage.googleapis.com/v1beta/models") +
            `?key=${encodeURIComponent(config.apiKey)}`;

          response = await fetch(endpoint, { method: "GET" });
          break;
        }

        case "lmstudio":
          response = await fetch(customEndpoint || "http://localhost:1234/v1/models", {
            method: "GET",
          });
          break;

        case "litellm":
          response = await fetch(customEndpoint || "http://localhost:4000/models", {
            method: "GET",
            headers: config.apiKey ? { Authorization: `Bearer ${config.apiKey}` } : {},
          });
          break;

        case "ollama":
          // Ollama has "tags" endpoint that can be used as a basic connectivity check
          response = await fetch(customEndpoint || "http://localhost:11434/api/tags", {
            method: "GET",
          });
          break;

        default:
          throw new Error(`Provider ${config.provider} is not supported for connection test.`);
      }

      if (response?.ok) {
        toast({
          title: "Connection Successful!",
          description: `Successfully connected to ${providerName}.",
        });
      } else {
        let errorMessage = "";
        try {
          const errorData = await response.json();
          errorMessage =
            errorData.error?.message ||
            errorData.error?.type ||
            JSON.stringify(errorData) ||
            response.statusText;
        } catch (err) {
          errorMessage = response?.statusText || "Unknown error.";
        }

        toast({
          title: "Connection Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (err: any) {
      toast({
        title: "Connection Error",
        description: err.message || String(err),
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            API Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="provider">API Provider</Label>
            <Select value={config.provider} onValueChange={handleProviderChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select API Provider" />
              </SelectTrigger>
              <SelectContent>
                {API_PROVIDERS.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{provider.name}</span>
                      {!provider.requiresKey && (
                        <Badge variant="secondary" className="ml-2">No Key Required</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedProvider?.requiresKey && (
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <div className="relative">
                  <Input
                  id="apiKey"
                  type={showKey ? "text" : "password"}
                  value={config.apiKey}
                  onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder={`Enter your ${selectedProvider?.name} API key`}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowKey(!showKey)}
                >
                  {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="model">Model</Label>
            <Select value={config.model} onValueChange={(model) => setConfig(prev => ({ ...prev, model }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select Model" />
              </SelectTrigger>
              <SelectContent>
                {selectedProvider?.models.map((model) => (
                  <SelectItem key={model} value={model}>
                    {model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="endpoint">Custom Endpoint (Optional)</Label>
            <Input
              id="endpoint"
              value={customEndpoint}
              onChange={(e) => setCustomEndpoint(e.target.value)}
              placeholder={selectedProvider?.endpoint}
            />
          </div>

          <div className="flex gap-2">
            <Button onClick={handleSaveConfig} className="flex-1">
              <Save className="h-4 w-4 mr-2" />
              Save Configuration
            </Button>
            <Button variant="outline" onClick={handleTestConnection}>
              Test Connection
            </Button>
          </div>
        </CardContent>
      </Card>

      {Object.keys(savedConfigs).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Saved Configurations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(savedConfigs).map(([providerId, savedConfig]) => {
                const provider = API_PROVIDERS.find(p => p.id === providerId);
                return (
                  <div key={providerId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{provider?.name}</div>
                      <div className="text-sm text-gray-500">
                        Model: {savedConfig.model}
                        {savedConfig.apiKey && <span className="ml-2">• Key saved</span>}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setConfig(savedConfig);
                          setCustomEndpoint(savedConfig.endpoint || provider?.endpoint || '');
                        }}
                      >
                        Load
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteConfig(providerId)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default APISettings;
