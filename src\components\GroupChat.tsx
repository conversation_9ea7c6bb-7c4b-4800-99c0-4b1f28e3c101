
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Send, X, Users, Bot } from 'lucide-react';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  senderColor: string;
  senderIcon: React.ComponentType<any>;
}

interface GroupChatProps {
  onClose: () => void;
}

const GroupChat = ({ onClose }: GroupChatProps) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      senderId: 'director',
      senderName: 'General Director',
      content: "Team, let's discuss our strategic priorities for the upcoming quarter. What are your thoughts?",
      timestamp: '2 min ago',
      senderColor: 'from-amber-500 to-orange-500',
      senderIcon: () => <span>👑</span>
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isGroupTyping, setIsGroupTyping] = useState(false);

  const agents = [
    { id: 'sales', name: 'Sarah Chen', color: 'from-green-500 to-emerald-600', icon: () => <span>📈</span> },
    { id: 'marketing', name: 'Mike Rodriguez', color: 'from-purple-500 to-pink-600', icon: () => <span>📢</span> },
    { id: 'finance', name: 'David Kim', color: 'from-blue-500 to-cyan-600', icon: () => <span>💰</span> },
    { id: 'it', name: 'Alex Thompson', color: 'from-slate-500 to-gray-600', icon: () => <span>💻</span> },
  ];

  const generateGroupResponses = (userMessage: string) => {
    const responses = [
      {
        agent: agents[0],
        content: "From a sales perspective, I see strong opportunities in the enterprise segment. Our pipeline indicates 25% growth potential."
      },
      {
        agent: agents[1],
        content: "Marketing data supports this. Brand awareness in the enterprise space has increased 40% this quarter."
      },
      {
        agent: agents[2],
        content: "Financially, we have the budget to support aggressive growth initiatives. ROI projections look promising."
      },
      {
        agent: agents[3],
        content: "Our infrastructure can scale to support increased enterprise demand. I recommend implementing additional security measures."
      }
    ];

    return responses;
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const userMsg: Message = {
      id: Date.now().toString(),
      senderId: 'user',
      senderName: 'You',
      content: newMessage,
      timestamp: 'now',
      senderColor: 'from-gray-500 to-gray-600',
      senderIcon: () => <span>👤</span>
    };

    setMessages(prev => [...prev, userMsg]);
    setNewMessage('');
    setIsGroupTyping(true);

    // Simulate group discussion
    const responses = generateGroupResponses(newMessage);
    
    responses.forEach((response, index) => {
      setTimeout(() => {
        const agentMessage: Message = {
          id: (Date.now() + index + 1).toString(),
          senderId: response.agent.id,
          senderName: response.agent.name,
          content: response.content,
          timestamp: 'now',
          senderColor: response.agent.color,
          senderIcon: response.agent.icon
        };
        
        setMessages(prev => [...prev, agentMessage]);
        
        if (index === responses.length - 1) {
          setIsGroupTyping(false);
        }
      }, (index + 1) * 2000);
    });
  };

  return (
    <Card className="fixed inset-4 z-50 bg-white shadow-2xl border-2">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Executive Team Chat</CardTitle>
              <Badge variant="secondary" className="text-xs">All Department Heads</Badge>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-96 p-0">
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.senderId === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className="flex items-start space-x-2 max-w-[80%]">
                {message.senderId !== 'user' && (
                  <Avatar className="h-8 w-8">
                    <div className={`bg-gradient-to-r ${message.senderColor} w-full h-full rounded-full flex items-center justify-center`}>
                      <message.senderIcon />
                    </div>
                    <AvatarFallback>{message.senderName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                )}
                <div className={`p-3 rounded-lg ${
                  message.senderId === 'user' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  {message.senderId !== 'user' && (
                    <div className="flex items-center gap-2 mb-1">
                      <Bot className="h-3 w-3" />
                      <span className="text-xs font-medium">{message.senderName}</span>
                    </div>
                  )}
                  <p className="text-sm">{message.content}</p>
                </div>
              </div>
            </div>
          ))}
          
          {isGroupTyping && (
            <div className="flex justify-start">
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <Users className="h-3 w-3" />
                  <span className="text-xs">Team is discussing...</span>
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="border-t p-4">
          <div className="flex space-x-2">
            <Textarea
              placeholder="Ask the entire team a question..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="min-h-[60px]"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || isGroupTyping}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GroupChat;
