// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tymjsvodytrngsllbyes.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR5bWpzdm9keXRybmdzbGxieWVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzNDI1MzgsImV4cCI6MjA2NTkxODUzOH0.-xdy52MppxrSWvZuIRrw_BGtdRRzbYJJD4tsmfTrvfY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);