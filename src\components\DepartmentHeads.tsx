
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { DollarSign, TrendingUp, Monitor, Megaphone, MessageCircle, Brain, Users, Settings, X } from 'lucide-react';
import ConsultationChat from './ConsultationChat';
import GroupChat from './GroupChat';
import APISettings from './APISettings';

const DepartmentHeads = () => {
  const [activeConsultation, setActiveConsultation] = useState<string | null>(null);
  const [showGroupChat, setShowGroupChat] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const agents = [
    {
      id: 'sales',
      name: '<PERSON>',
      title: 'Director of Sales',
      icon: TrendingUp,
      color: 'from-green-500 to-emerald-600',
      borderColor: 'border-l-green-500',
      badgeColor: 'bg-green-100 text-green-800',
      expertise: ['Revenue Growth', 'Customer Acquisition', 'Sales Funnels', 'CRM Strategy'],
      personality: 'Results-driven and persuasive. Focuses on numbers and conversion optimization.',
      recentInsight: 'Customer retention could improve by 23% with a loyalty program implementation.'
    },
    {
      id: 'marketing',
      name: 'Mike Rodriguez',
      title: 'Director of Marketing',
      icon: Megaphone,
      color: 'from-purple-500 to-pink-600',
      borderColor: 'border-l-purple-500',
      badgeColor: 'bg-purple-100 text-purple-800',
      expertise: ['Brand Strategy', 'Digital Marketing', 'Content Creation', 'Market Research'],
      personality: 'Creative and analytical. Balances brand vision with data-driven decisions.',
      recentInsight: 'Social media engagement is 40% higher on video content vs. static posts.'
    },
    {
      id: 'finance',
      name: 'David Kim',
      title: 'Chief Financial Officer',
      icon: DollarSign,
      color: 'from-blue-500 to-cyan-600',
      borderColor: 'border-l-blue-500',
      badgeColor: 'bg-blue-100 text-blue-800',
      expertise: ['Financial Planning', 'Budget Management', 'Risk Assessment', 'Investment Strategy'],
      personality: 'Methodical and prudent. Emphasizes financial stability and strategic investments.',
      recentInsight: 'Cash flow optimization could free up $50K for growth initiatives this quarter.'
    },
    {
      id: 'it',
      name: 'Alex Thompson',
      title: 'Director of IT',
      icon: Monitor,
      color: 'from-slate-500 to-gray-600',
      borderColor: 'border-l-slate-500',
      badgeColor: 'bg-slate-100 text-slate-800',
      expertise: ['System Architecture', 'Cybersecurity', 'Process Automation', 'Tech Strategy'],
      personality: 'Logical and security-focused. Prioritizes scalability and system reliability.',
      recentInsight: 'Implementing automation could reduce manual tasks by 60% and save 15 hours/week.'
    }
  ];

  const handleConsult = (agentId: string) => {
    setActiveConsultation(agentId);
  };

  const activeAgent = agents.find(agent => agent.id === activeConsultation);

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Your Executive Team</h2>
        <p className="text-gray-600 mb-4">Specialized AI agents ready to provide expert guidance</p>
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => setShowGroupChat(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            <Users className="h-4 w-4 mr-2" />
            Start Group Discussion
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            API Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {agents.map((agent) => (
          <Card key={agent.id} className={`border-l-4 ${agent.borderColor} hover:shadow-lg transition-shadow duration-200`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <div className={`bg-gradient-to-r ${agent.color} w-full h-full rounded-full flex items-center justify-center`}>
                      <agent.icon className="h-6 w-6 text-white" />
                    </div>
                    <AvatarFallback>{agent.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <Badge className={agent.badgeColor}>{agent.title}</Badge>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleConsult(agent.id)}
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Consult
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-sm text-gray-700 mb-2">Expertise</h4>
                <div className="flex flex-wrap gap-2">
                  {agent.expertise.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Brain className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Latest Insight</span>
                </div>
                <p className="text-sm text-gray-600">{agent.recentInsight}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Individual Consultation Chat */}
      {activeConsultation && activeAgent && (
        <ConsultationChat
          agent={activeAgent}
          onClose={() => setActiveConsultation(null)}
          onOpenSettings={() => setShowSettings(true)}
        />
      )}

      {/* Group Chat */}
      {showGroupChat && (
        <GroupChat onClose={() => setShowGroupChat(false)} />
      )}

      {/* API Settings */}
      {showSettings && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">API Configuration</h2>
                <Button variant="ghost" onClick={() => setShowSettings(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <APISettings />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DepartmentHeads;
